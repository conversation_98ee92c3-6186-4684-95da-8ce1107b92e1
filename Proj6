import math
print("Area and Perimeter Calculator\nPlease select your shape\n\n1.Square\n2.Rectangle\n3.Circle\n\n")
while True:
    x = int(input("Your Shape Number: "))
    if x == 1:
        s = int(input("The length of the side of Square is: ",))
        print("\nThe Perimeter of Square is ", s*4, "\nThe Area of Square is ", pow(s,2))
        break
    elif x == 2:
        l = int(input("The length of Rectangle is: "))
        b = int(input("The breadth of Rectangle is: "))
        print("\nThe Perimeter of Rectangle is: ", 2*(l+b), "\nThe Area of Rectangle is: ", l*b)
        break
    elif x == 3:
        r = int(input("The radius of the Circle is: "))
        print("\nThe Perimeter of Circle is: ", round(math.pi*2*r, 2), "\nThe Area of Circle is: ", round(math.pi*(r**2),2))
        break
    else:
        print("Invalid input, please input a valid number\n")
        #missed a comma in line 14 leading to a syntax error, always comma when separating quoted lines and variables/calcs
        #can clear the console after a successful calculation, promt user to press any key to exit
        #make the menu print a function so that it can be called wherever required without reusing code 99x or using a loop.