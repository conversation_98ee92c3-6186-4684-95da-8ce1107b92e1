print("Which operation would you like to perform?\n\n1.Addition\n2.Subtraction\n3.Multiplication\n4.Division\n5.Modulo Function/Remainder function\n6.Integral Division\n7.Exponential Function\n8.Exit the Calculator\n")
def invalid(y):
        while True:
            if y != 0:
                break
            else:
                print("You cannot divide by zero, please try again.")
                y = int(input("Your Second Number is: "))
while True:
     func = int(input("Operation: "))
     x = int(input("Your First Number is: "))
     y = int(input("Your Second Number is: "))
     match func:
        case 1:
            print("The Sum of the Two Numbers is: ", x+y)
        case 2:
            print("The Sum of the Two Numbers is: ", x-y)
        case 3:
            print("The Sum of the Two Numbers is: ", x*y)
        case 4:
             print("The Sum of the Two Numbers is: ", x/y)